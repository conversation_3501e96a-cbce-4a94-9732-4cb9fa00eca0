{"AbsoluteOrientationSensor": {"inherits": "OrientationSensor", "implements": []}, "AbstractRange": {"inherits": null, "implements": []}, "Accelerometer": {"inherits": "Sensor", "implements": []}, "AmbientLightSensor": {"inherits": "Sensor", "implements": []}, "AnalyserNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "AnimationEvent": {"inherits": "Event", "implements": []}, "ArchiveRequest": {"inherits": "DOMRequest", "implements": []}, "Attr": {"inherits": "Node", "implements": []}, "AudioBufferSourceNode": {"inherits": "AudioScheduledSourceNode", "implements": ["AudioNodePassThrough"]}, "AudioChannelManager": {"inherits": "EventTarget", "implements": []}, "AudioContext": {"inherits": "BaseAudioContext", "implements": []}, "AudioDestinationNode": {"inherits": "AudioNode", "implements": []}, "AudioNode": {"inherits": "EventTarget", "implements": []}, "AudioProcessingEvent": {"inherits": "Event", "implements": []}, "AudioScheduledSourceNode": {"inherits": "AudioNode", "implements": []}, "AudioStreamTrack": {"inherits": "MediaStreamTrack", "implements": []}, "AudioTrackList": {"inherits": "EventTarget", "implements": []}, "AutocompleteErrorEvent": {"inherits": "Event", "implements": []}, "BarProp": {"inherits": null, "implements": []}, "BaseAudioContext": {"inherits": "EventTarget", "implements": []}, "BatteryManager": {"inherits": "EventTarget", "implements": []}, "BeforeAfterKeyboardEvent": {"inherits": "KeyboardEvent", "implements": []}, "BeforeInstallPromptEvent": {"inherits": "Event", "implements": []}, "BeforeUnloadEvent": {"inherits": "Event", "implements": []}, "BiquadFilterNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "BlobEvent": {"inherits": "Event", "implements": []}, "BluetoothAdapter": {"inherits": "EventTarget", "implements": []}, "BluetoothAdapterEvent": {"inherits": "Event", "implements": []}, "BluetoothAttributeEvent": {"inherits": "Event", "implements": []}, "BluetoothDevice": {"inherits": "EventTarget", "implements": []}, "BluetoothDeviceEvent": {"inherits": "Event", "implements": []}, "BluetoothDiscoveryHandle": {"inherits": "EventTarget", "implements": []}, "BluetoothDiscoveryStateChangedEvent": {"inherits": "Event", "implements": []}, "BluetoothGatt": {"inherits": "EventTarget", "implements": []}, "BluetoothManager": {"inherits": "EventTarget", "implements": []}, "BluetoothPairingEvent": {"inherits": "Event", "implements": []}, "BluetoothStatusChangedEvent": {"inherits": "Event", "implements": []}, "BoxObject": {"inherits": null, "implements": []}, "BroadcastChannel": {"inherits": "EventTarget", "implements": []}, "BrowserElement": {"inherits": null, "implements": ["Browser<PERSON><PERSON><PERSON><PERSON><PERSON>", "BrowserElementPrivileged"]}, "CDATASection": {"inherits": "Text", "implements": []}, "CFStateChangeEvent": {"inherits": "Event", "implements": []}, "CSSCounterStyleRule": {"inherits": "CSSRule", "implements": []}, "CSSFontFaceLoadEvent": {"inherits": "Event", "implements": []}, "CSSImageValue": {"inherits": "CSSStyleValue", "implements": []}, "CSSKeywordValue": {"inherits": "CSSStyleValue", "implements": []}, "CSSMathInvert": {"inherits": "CSSMathValue", "implements": []}, "CSSMathMax": {"inherits": "CSSMathValue", "implements": []}, "CSSMathMin": {"inherits": "CSSMathValue", "implements": []}, "CSSMathNegate": {"inherits": "CSSMathValue", "implements": []}, "CSSMathProduct": {"inherits": "CSSMathValue", "implements": []}, "CSSMathSum": {"inherits": "CSSMathValue", "implements": []}, "CSSMathValue": {"inherits": "CSSNumericValue", "implements": ["CSSMathInvert", "CSSMathMax", "CSSMathMin", "CSSMathNegate", "CSSMathProduct", "CSSMathSum"]}, "CSSMatrixComponent": {"inherits": "CSSTransformComponent", "implements": []}, "CSSNumericValue": {"inherits": "CSSStyleValue", "implements": ["CSSMathValue", "CSSUnitValue"]}, "CSSPerspective": {"inherits": "CSSTransformComponent", "implements": []}, "CSSPositionValue": {"inherits": "CSSStyleValue", "implements": []}, "CSSPrimitiveValue": {"inherits": "CSSValue", "implements": []}, "CSSPseudoElement": {"inherits": "EventTarget", "implements": ["Animatable"]}, "CSSRotate": {"inherits": "CSSTransformComponent", "implements": []}, "CSSScale": {"inherits": "CSSTransformComponent", "implements": []}, "CSSSkew": {"inherits": "CSSTransformComponent", "implements": []}, "CSSSkewX": {"inherits": "CSSTransformComponent", "implements": []}, "CSSSkewY": {"inherits": "CSSTransformComponent", "implements": []}, "CSSStyleDeclaration": {"inherits": null, "implements": []}, "CSSStyleSheet": {"inherits": "StyleSheet", "implements": []}, "CSSStyleValue": {"inherits": null, "implements": ["CSSImageValue", "CSSKeywordValue", "CSSNumericValue", "CSSPositionValue", "CSSTransformValue", "CSSUnitValue", "CSSUnparsedValue"]}, "CSSTransformComponent": {"inherits": null, "implements": ["CSSMatrixComponent", "CSSPerspective", "CSSRotate", "CSSScale", "CSSSkew", "CSSSkewX", "CSSSkewY", "CSSTranslate"]}, "CSSTransformValue": {"inherits": "CSSStyleValue", "implements": []}, "CSSTranslate": {"inherits": "CSSTransformComponent", "implements": []}, "CSSUnitValue": {"inherits": "CSSNumericValue", "implements": []}, "CSSUnparsedValue": {"inherits": "CSSStyleValue", "implements": []}, "CSSValueList": {"inherits": "CSSValue", "implements": []}, "CallEvent": {"inherits": "Event", "implements": []}, "CallGroupErrorEvent": {"inherits": "Event", "implements": []}, "CameraClosedEvent": {"inherits": "Event", "implements": []}, "CameraConfigurationEvent": {"inherits": "Event", "implements": []}, "CameraControl": {"inherits": "MediaStream", "implements": []}, "CameraFacesDetectedEvent": {"inherits": "Event", "implements": []}, "CameraStateChangeEvent": {"inherits": "Event", "implements": []}, "CanvasCaptureMediaStream": {"inherits": "MediaStream", "implements": []}, "CaretPosition": {"inherits": null, "implements": []}, "ChannelMergerNode": {"inherits": "AudioNode", "implements": []}, "ChannelSplitterNode": {"inherits": "AudioNode", "implements": []}, "CharacterData": {"inherits": "Node", "implements": ["ChildNode", "NonDocumentTypeChildNode"]}, "ChromeWorker": {"inherits": "Worker", "implements": []}, "Clipboard": {"inherits": "EventTarget", "implements": []}, "ClipboardEvent": {"inherits": "Event", "implements": []}, "CloseEvent": {"inherits": "Event", "implements": []}, "CommandEvent": {"inherits": "Event", "implements": []}, "Comment": {"inherits": "CharacterData", "implements": []}, "CompositionEvent": {"inherits": "UIEvent", "implements": []}, "ConstantSourceNode": {"inherits": "AudioScheduledSourceNode", "implements": []}, "ContactManager": {"inherits": "EventTarget", "implements": []}, "ContainerBoxObject": {"inherits": "BoxObject", "implements": []}, "ConvolverNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "Crypto": {"inherits": null, "implements": []}, "CustomEvent": {"inherits": "Event", "implements": []}, "DOMApplication": {"inherits": "EventTarget", "implements": []}, "DOMApplicationsManager": {"inherits": "EventTarget", "implements": []}, "DOMCursor": {"inherits": "EventTarget", "implements": ["DOMRequestShared"]}, "DOMDownload": {"inherits": "EventTarget", "implements": []}, "DOMDownloadManager": {"inherits": "EventTarget", "implements": []}, "DOMException": {"inherits": null, "implements": ["ExceptionMembers"]}, "DOMImplementation": {"inherits": null, "implements": []}, "DOMMatrix": {"inherits": "DOMMatrixReadOnly", "implements": []}, "DOMMobileMessageError": {"inherits": "DOMError", "implements": []}, "DOMParser": {"inherits": null, "implements": []}, "DOMPoint": {"inherits": "DOMPointReadOnly", "implements": []}, "DOMRect": {"inherits": "DOMRectReadOnly", "implements": []}, "DOMRequest": {"inherits": "EventTarget", "implements": ["DOMRequestShared"]}, "DOMSettableTokenList": {"inherits": "DOMTokenList", "implements": []}, "DOMStringMap": {"inherits": null, "implements": []}, "DOMTokenList": {"inherits": null, "implements": []}, "DOMTransactionEvent": {"inherits": "Event", "implements": []}, "DataContainerEvent": {"inherits": "Event", "implements": []}, "DataErrorEvent": {"inherits": "Event", "implements": []}, "DataStore": {"inherits": "EventTarget", "implements": []}, "DataStoreChangeEvent": {"inherits": "Event", "implements": []}, "DedicatedWorkerGlobalScope": {"inherits": "WorkerGlobalScope", "implements": []}, "DelayNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "DesktopNotification": {"inherits": "EventTarget", "implements": []}, "DeviceLightEvent": {"inherits": "Event", "implements": []}, "DeviceMotionEvent": {"inherits": "Event", "implements": []}, "DeviceOrientationEvent": {"inherits": "Event", "implements": []}, "DeviceProximityEvent": {"inherits": "Event", "implements": []}, "DeviceStorage": {"inherits": "EventTarget", "implements": []}, "DeviceStorageChangeEvent": {"inherits": "Event", "implements": []}, "Document": {"inherits": "Node", "implements": ["FontFaceSource", "GeometryUtils", "GlobalEventHandlers", "OnErrorEventHandlerForNodes", "ParentNode", "TouchEventHandlers", "XPathEvaluator"]}, "DocumentFragment": {"inherits": "Node", "implements": ["ParentNode"]}, "DocumentType": {"inherits": "Node", "implements": ["ChildNode"]}, "DownloadEvent": {"inherits": "Event", "implements": []}, "DragEvent": {"inherits": "MouseEvent", "implements": []}, "DynamicsCompressorNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "Element": {"inherits": "Node", "implements": ["Animatable", "ChildNode", "GeometryUtils", "NonDocumentTypeChildNode", "ParentNode"]}, "EngineeringMode": {"inherits": "EventTarget", "implements": []}, "ErrorEvent": {"inherits": "Event", "implements": []}, "Event": {"inherits": null, "implements": []}, "EventSource": {"inherits": "EventTarget", "implements": []}, "Exception": {"inherits": null, "implements": ["ExceptionMembers"]}, "ExtendableEvent": {"inherits": "Event", "implements": []}, "FMRadio": {"inherits": "EventTarget", "implements": []}, "FetchEvent": {"inherits": "Event", "implements": []}, "File": {"inherits": "Blob", "implements": []}, "FileList": {"inherits": null, "implements": []}, "FileReader": {"inherits": "EventTarget", "implements": []}, "FocusEvent": {"inherits": "UIEvent", "implements": []}, "FontFaceSet": {"inherits": "EventTarget", "implements": []}, "FormData": {"inherits": null, "implements": []}, "GainNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "GamepadAxisMoveEvent": {"inherits": "GamepadEvent", "implements": []}, "GamepadButtonEvent": {"inherits": "GamepadEvent", "implements": []}, "GamepadEvent": {"inherits": "Event", "implements": []}, "Gyroscope": {"inherits": "Sensor", "implements": []}, "HMDVRDevice": {"inherits": "VRDevice", "implements": []}, "HTMLAnchorElement": {"inherits": "HTMLElement", "implements": ["HTMLHyperlinkElementUtils", "URLUtilsSearchParams"]}, "HTMLAppletElement": {"inherits": "HTMLElement", "implements": ["MozFrameLoaderOwner", "MozImageLoadingContent", "MozObjectLoadingContent"]}, "HTMLAreaElement": {"inherits": "HTMLElement", "implements": ["HTMLHyperlinkElementUtils", "URLUtilsSearchParams"]}, "HTMLAudioElement": {"inherits": "HTMLMediaElement", "implements": []}, "HTMLBRElement": {"inherits": "HTMLElement", "implements": []}, "HTMLBaseElement": {"inherits": "HTMLElement", "implements": []}, "HTMLBodyElement": {"inherits": "HTMLElement", "implements": ["WindowEventHandlers"]}, "HTMLButtonElement": {"inherits": "HTMLElement", "implements": []}, "HTMLCanvasElement": {"inherits": "HTMLElement", "implements": []}, "HTMLCollection": {"inherits": null, "implements": []}, "HTMLContentElement": {"inherits": "HTMLElement", "implements": []}, "HTMLDListElement": {"inherits": "HTMLElement", "implements": []}, "HTMLDataElement": {"inherits": "HTMLElement", "implements": []}, "HTMLDataListElement": {"inherits": "HTMLElement", "implements": []}, "HTMLDirectoryElement": {"inherits": "HTMLElement", "implements": []}, "HTMLDivElement": {"inherits": "HTMLElement", "implements": []}, "HTMLDocument": {"inherits": "Document", "implements": []}, "HTMLElement": {"inherits": "Element", "implements": ["GlobalEventHandlers", "OnErrorEventHandlerForNodes", "TouchEventHandlers"]}, "HTMLEmbedElement": {"inherits": "HTMLElement", "implements": ["MozFrameLoaderOwner", "MozImageLoadingContent", "MozObjectLoadingContent"]}, "HTMLFieldSetElement": {"inherits": "HTMLElement", "implements": []}, "HTMLFontElement": {"inherits": "HTMLElement", "implements": []}, "HTMLFormControlsCollection": {"inherits": "HTMLCollection", "implements": []}, "HTMLFormElement": {"inherits": "HTMLElement", "implements": []}, "HTMLFrameElement": {"inherits": "HTMLElement", "implements": ["MozFrameLoaderOwner"]}, "HTMLFrameSetElement": {"inherits": "HTMLElement", "implements": ["WindowEventHandlers"]}, "HTMLHRElement": {"inherits": "HTMLElement", "implements": []}, "HTMLHeadElement": {"inherits": "HTMLElement", "implements": []}, "HTMLHeadingElement": {"inherits": "HTMLElement", "implements": []}, "HTMLHtmlElement": {"inherits": "HTMLElement", "implements": []}, "HTMLIFrameElement": {"inherits": "HTMLElement", "implements": ["BrowserElement", "MozFrameLoaderOwner"]}, "HTMLImageElement": {"inherits": "HTMLElement", "implements": ["MozImageLoadingContent"]}, "HTMLInputElement": {"inherits": "HTMLElement", "implements": ["MozImageLoadingContent", "MozPhonetic"]}, "HTMLLIElement": {"inherits": "HTMLElement", "implements": []}, "HTMLLabelElement": {"inherits": "HTMLElement", "implements": []}, "HTMLLegendElement": {"inherits": "HTMLElement", "implements": []}, "HTMLLinkElement": {"inherits": "HTMLElement", "implements": ["LinkStyle"]}, "HTMLMapElement": {"inherits": "HTMLElement", "implements": []}, "HTMLMarqueeElement": {"inherits": "HTMLElement", "implements": []}, "HTMLMediaElement": {"inherits": "HTMLElement", "implements": []}, "HTMLMenuElement": {"inherits": "HTMLElement", "implements": []}, "HTMLMenuItemElement": {"inherits": "HTMLElement", "implements": []}, "HTMLMetaElement": {"inherits": "HTMLElement", "implements": []}, "HTMLMeterElement": {"inherits": "HTMLElement", "implements": []}, "HTMLModElement": {"inherits": "HTMLElement", "implements": []}, "HTMLOListElement": {"inherits": "HTMLElement", "implements": []}, "HTMLObjectElement": {"inherits": "HTMLElement", "implements": ["MozFrameLoaderOwner", "MozImageLoadingContent", "MozObjectLoadingContent"]}, "HTMLOptGroupElement": {"inherits": "HTMLElement", "implements": []}, "HTMLOptionElement": {"inherits": "HTMLElement", "implements": []}, "HTMLOptionsCollection": {"inherits": "HTMLCollection", "implements": []}, "HTMLOutputElement": {"inherits": "HTMLElement", "implements": []}, "HTMLParagraphElement": {"inherits": "HTMLElement", "implements": []}, "HTMLParamElement": {"inherits": "HTMLElement", "implements": []}, "HTMLPictureElement": {"inherits": "HTMLElement", "implements": []}, "HTMLPreElement": {"inherits": "HTMLElement", "implements": []}, "HTMLProgressElement": {"inherits": "HTMLElement", "implements": []}, "HTMLPropertiesCollection": {"inherits": "HTMLCollection", "implements": []}, "HTMLQuoteElement": {"inherits": "HTMLElement", "implements": []}, "HTMLScriptElement": {"inherits": "HTMLElement", "implements": []}, "HTMLSelectElement": {"inherits": "HTMLElement", "implements": []}, "HTMLShadowElement": {"inherits": "HTMLElement", "implements": []}, "HTMLSourceElement": {"inherits": "HTMLElement", "implements": []}, "HTMLSpanElement": {"inherits": "HTMLElement", "implements": []}, "HTMLStyleElement": {"inherits": "HTMLElement", "implements": ["LinkStyle"]}, "HTMLTableCaptionElement": {"inherits": "HTMLElement", "implements": []}, "HTMLTableCellElement": {"inherits": "HTMLElement", "implements": []}, "HTMLTableColElement": {"inherits": "HTMLElement", "implements": []}, "HTMLTableElement": {"inherits": "HTMLElement", "implements": []}, "HTMLTableRowElement": {"inherits": "HTMLElement", "implements": []}, "HTMLTableSectionElement": {"inherits": "HTMLElement", "implements": []}, "HTMLTemplateElement": {"inherits": "HTMLElement", "implements": []}, "HTMLTextAreaElement": {"inherits": "HTMLElement", "implements": []}, "HTMLTimeElement": {"inherits": "HTMLElement", "implements": []}, "HTMLTitleElement": {"inherits": "HTMLElement", "implements": []}, "HTMLTrackElement": {"inherits": "HTMLElement", "implements": []}, "HTMLUListElement": {"inherits": "HTMLElement", "implements": []}, "HTMLUnknownElement": {"inherits": "HTMLElement", "implements": []}, "HTMLVideoElement": {"inherits": "HTMLMediaElement", "implements": []}, "HashChangeEvent": {"inherits": "Event", "implements": []}, "History": {"inherits": null, "implements": []}, "IDBCursorWithValue": {"inherits": "IDBCursor", "implements": []}, "IDBDatabase": {"inherits": "EventTarget", "implements": []}, "IDBFileHandle": {"inherits": "EventTarget", "implements": []}, "IDBFileRequest": {"inherits": "DOMRequest", "implements": []}, "IDBMutableFile": {"inherits": "EventTarget", "implements": []}, "IDBOpenDBRequest": {"inherits": "IDBRequest", "implements": []}, "IDBRequest": {"inherits": "EventTarget", "implements": []}, "IDBTransaction": {"inherits": "EventTarget", "implements": []}, "IDBVersionChangeEvent": {"inherits": "Event", "implements": []}, "IccCardLockError": {"inherits": "DOMError", "implements": []}, "IccChangeEvent": {"inherits": "Event", "implements": []}, "ImageCapture": {"inherits": "EventTarget", "implements": []}, "ImageCaptureErrorEvent": {"inherits": "Event", "implements": []}, "ImageDocument": {"inherits": "HTMLDocument", "implements": []}, "InputEvent": {"inherits": "UIEvent", "implements": []}, "InstallEvent": {"inherits": "ExtendableEvent", "implements": []}, "InstallTrigger": {"inherits": null, "implements": []}, "KeyboardEvent": {"inherits": "UIEvent", "implements": ["KeyEvent"]}, "LinearAccelerationSensor": {"inherits": "Accelerometer", "implements": []}, "ListBoxObject": {"inherits": "BoxObject", "implements": []}, "LocalMediaStream": {"inherits": "MediaStream", "implements": []}, "Location": {"inherits": null, "implements": []}, "Magnetometer": {"inherits": "Sensor", "implements": []}, "MediaDevices": {"inherits": "EventTarget", "implements": []}, "MediaElementAudioSourceNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "MediaEncryptedEvent": {"inherits": "Event", "implements": []}, "MediaKeyError": {"inherits": "Event", "implements": []}, "MediaKeyMessageEvent": {"inherits": "Event", "implements": []}, "MediaKeySession": {"inherits": "EventTarget", "implements": []}, "MediaRecorder": {"inherits": "EventTarget", "implements": []}, "MediaSource": {"inherits": "EventTarget", "implements": []}, "MediaStream": {"inherits": "EventTarget", "implements": []}, "MediaStreamAudioDestinationNode": {"inherits": "AudioNode", "implements": []}, "MediaStreamAudioSourceNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "MediaStreamEvent": {"inherits": "Event", "implements": []}, "MediaStreamTrackEvent": {"inherits": "Event", "implements": []}, "MenuBoxObject": {"inherits": "BoxObject", "implements": []}, "MerchantValidationEvent": {"inherits": "Event", "implements": []}, "MessageEvent": {"inherits": "Event", "implements": []}, "MessagePort": {"inherits": "EventTarget", "implements": ["Transferable"]}, "MimeTypeArray": {"inherits": null, "implements": []}, "MouseEvent": {"inherits": "UIEvent", "implements": []}, "MouseScrollEvent": {"inherits": "MouseEvent", "implements": []}, "MozAbortablePromise": {"inherits": "_Promise", "implements": []}, "MozActivity": {"inherits": "DOMRequest", "implements": []}, "MozApplicationEvent": {"inherits": "Event", "implements": []}, "MozCdmaIccInfo": {"inherits": "MozIccInfo", "implements": []}, "MozCellBroadcast": {"inherits": "EventTarget", "implements": []}, "MozCellBroadcastEvent": {"inherits": "Event", "implements": []}, "MozClirModeEvent": {"inherits": "Event", "implements": []}, "MozContactChangeEvent": {"inherits": "Event", "implements": []}, "MozEmergencyCbModeEvent": {"inherits": "Event", "implements": []}, "MozGsmIccInfo": {"inherits": "MozIccInfo", "implements": []}, "MozIcc": {"inherits": "EventTarget", "implements": []}, "MozIccManager": {"inherits": "EventTarget", "implements": []}, "MozInputMethod": {"inherits": "EventTarget", "implements": []}, "MozInterAppMessageEvent": {"inherits": "Event", "implements": []}, "MozInterAppMessagePort": {"inherits": "EventTarget", "implements": []}, "MozMessageDeletedEvent": {"inherits": "Event", "implements": []}, "MozMmsEvent": {"inherits": "Event", "implements": []}, "MozMobileConnection": {"inherits": "EventTarget", "implements": []}, "MozMobileMessageManager": {"inherits": "EventTarget", "implements": []}, "MozNFC": {"inherits": "EventTarget", "implements": ["MozNFCManager"]}, "MozNFCPeerEvent": {"inherits": "Event", "implements": []}, "MozNFCTagEvent": {"inherits": "Event", "implements": []}, "MozOtaStatusEvent": {"inherits": "Event", "implements": []}, "MozSettingsEvent": {"inherits": "Event", "implements": []}, "MozSettingsTransactionEvent": {"inherits": "Event", "implements": []}, "MozSmsEvent": {"inherits": "Event", "implements": []}, "MozSpeakerManager": {"inherits": "EventTarget", "implements": []}, "MozStkCommandEvent": {"inherits": "Event", "implements": []}, "MozVoicemail": {"inherits": "EventTarget", "implements": []}, "MozVoicemailEvent": {"inherits": "Event", "implements": []}, "MozWifiConnectionInfoEvent": {"inherits": "Event", "implements": []}, "MozWifiManager": {"inherits": "EventTarget", "implements": []}, "MozWifiStationInfoEvent": {"inherits": "Event", "implements": []}, "MozWifiStatusChangeEvent": {"inherits": "Event", "implements": []}, "MutationEvent": {"inherits": "Event", "implements": []}, "MutationObserver": {"inherits": null, "implements": []}, "MutationRecord": {"inherits": null, "implements": []}, "NamedNodeMap": {"inherits": null, "implements": []}, "Navigator": {"inherits": null, "implements": ["NavigatorBattery", "NavigatorC<PERSON>nt<PERSON><PERSON>s", "NavigatorDataStore", "NavigatorFeatures", "NavigatorGeolocation", "NavigatorID", "NavigatorLanguage", "NavigatorMobileId", "NavigatorOnLine", "NavigatorStorageUtils"]}, "NetworkInformation": {"inherits": "EventTarget", "implements": []}, "Node": {"inherits": "EventTarget", "implements": []}, "NodeIterator": {"inherits": null, "implements": []}, "NodeList": {"inherits": null, "implements": []}, "Notification": {"inherits": "EventTarget", "implements": []}, "NotifyPaintEvent": {"inherits": "Event", "implements": []}, "OfflineAudioCompletionEvent": {"inherits": "Event", "implements": []}, "OfflineAudioContext": {"inherits": "BaseAudioContext", "implements": []}, "OfflineResourceList": {"inherits": "EventTarget", "implements": []}, "OffscreenCanvas": {"inherits": "EventTarget", "implements": []}, "OrientationSensor": {"inherits": "Sensor", "implements": []}, "OscillatorNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "PageTransitionEvent": {"inherits": "Event", "implements": []}, "PaintRequest": {"inherits": null, "implements": []}, "PaintRequestList": {"inherits": null, "implements": []}, "PannerNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "PaymentAddress": {"inherits": null, "implements": []}, "PaymentMethodChangeEvent": {"inherits": "PaymentRequestUpdateEvent", "implements": []}, "PaymentRequest": {"inherits": "EventTarget", "implements": []}, "PaymentRequestUpdateEvent": {"inherits": "Event", "implements": []}, "PaymentResponse": {"inherits": "EventTarget", "implements": []}, "Performance": {"inherits": null, "implements": []}, "PerformanceLongTaskTiming": {"inherits": "PerformanceEntry", "implements": []}, "PerformanceMark": {"inherits": "PerformanceEntry", "implements": []}, "PerformanceMeasure": {"inherits": "PerformanceEntry", "implements": []}, "PerformanceNavigationTiming": {"inherits": "PerformanceEntry", "implements": []}, "PerformancePaintTiming": {"inherits": "PerformanceEntry", "implements": []}, "PerformanceResourceTiming": {"inherits": "PerformanceEntry", "implements": []}, "Plugin": {"inherits": null, "implements": []}, "PluginArray": {"inherits": null, "implements": []}, "PluginCrashedEvent": {"inherits": "Event", "implements": []}, "PointerEvent": {"inherits": "MouseEvent", "implements": []}, "PopStateEvent": {"inherits": "Event", "implements": []}, "PopupBlockedEvent": {"inherits": "Event", "implements": []}, "PopupBoxObject": {"inherits": "BoxObject", "implements": []}, "PositionSensorVRDevice": {"inherits": "VRDevice", "implements": []}, "PresentationDeviceInfoManager": {"inherits": "EventTarget", "implements": []}, "ProcessingInstruction": {"inherits": "CharacterData", "implements": []}, "ProgressEvent": {"inherits": "Event", "implements": []}, "PromiseRejectionEvent": {"inherits": "Event", "implements": []}, "PropertyNodeList": {"inherits": "NodeList", "implements": []}, "PseudoElement": {"inherits": null, "implements": ["GeometryUtils"]}, "RTCDTMFSender": {"inherits": "EventTarget", "implements": []}, "RTCDTMFToneChangeEvent": {"inherits": "Event", "implements": []}, "RTCDataChannel": {"inherits": "EventTarget", "implements": []}, "RTCDataChannelEvent": {"inherits": "Event", "implements": []}, "RTCDtlsTransport": {"inherits": "EventTarget", "implements": []}, "RTCIceTransport": {"inherits": "EventTarget", "implements": []}, "RTCPeerConnection": {"inherits": "EventTarget", "implements": []}, "RTCPeerConnectionIceEvent": {"inherits": "Event", "implements": []}, "RTCPeerConnectionIdentityErrorEvent": {"inherits": "Event", "implements": []}, "RTCPeerConnectionIdentityEvent": {"inherits": "Event", "implements": []}, "RTCSctpTransport": {"inherits": "EventTarget", "implements": []}, "RTCTrackEvent": {"inherits": "Event", "implements": []}, "RadioNodeList": {"inherits": "NodeList", "implements": []}, "Range": {"inherits": "AbstractRange", "implements": []}, "RecordErrorEvent": {"inherits": "Event", "implements": []}, "Rect": {"inherits": null, "implements": []}, "RelativeOrientationSensor": {"inherits": "OrientationSensor", "implements": []}, "Request": {"inherits": null, "implements": ["Body"]}, "Response": {"inherits": null, "implements": ["Body"]}, "StaticRange": {"inherits": "AbstractRange", "implements": []}, "SVGAElement": {"inherits": "SVGGraphicsElement", "implements": ["SVGURIReference"]}, "SVGAltGlyphElement": {"inherits": "SVGTextPositioningElement", "implements": ["SVGURIReference"]}, "SVGAnimateElement": {"inherits": "SVGAnimationElement", "implements": []}, "SVGAnimateMotionElement": {"inherits": "SVGAnimationElement", "implements": []}, "SVGAnimateTransformElement": {"inherits": "SVGAnimationElement", "implements": []}, "SVGAnimatedEnumeration": {"inherits": null, "implements": []}, "SVGAnimatedInteger": {"inherits": null, "implements": []}, "SVGAnimatedNumber": {"inherits": null, "implements": []}, "SVGAnimatedNumberList": {"inherits": null, "implements": []}, "SVGAnimatedPreserveAspectRatio": {"inherits": null, "implements": []}, "SVGAnimatedString": {"inherits": null, "implements": []}, "SVGAnimationElement": {"inherits": "SVGElement", "implements": ["SVGTests"]}, "SVGCircleElement": {"inherits": "SVGGeometryElement", "implements": []}, "SVGClipPathElement": {"inherits": "SVGElement", "implements": ["SVGUnitTypes"]}, "SVGComponentTransferFunctionElement": {"inherits": "SVGElement", "implements": []}, "SVGCursorElement": {"inherits": "SVGElement", "implements": ["SVGURIReference"]}, "SVGDefsElement": {"inherits": "SVGGraphicsElement", "implements": []}, "SVGDescElement": {"inherits": "SVGElement", "implements": []}, "SVGDocument": {"inherits": "Document", "implements": []}, "SVGElement": {"inherits": "Element", "implements": ["GlobalEventHandlers", "OnErrorEventHandlerForNodes", "TouchEventHandlers"]}, "SVGEllipseElement": {"inherits": "SVGGeometryElement", "implements": []}, "SVGFEBlendElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGFEColorMatrixElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGFEComponentTransferElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGFECompositeElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGFEConvolveMatrixElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGFEDiffuseLightingElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGFEDisplacementMapElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGFEDistantLightElement": {"inherits": "SVGElement", "implements": []}, "SVGFEDropShadowElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGFEFloodElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGFEFuncAElement": {"inherits": "SVGComponentTransferFunctionElement", "implements": []}, "SVGFEFuncBElement": {"inherits": "SVGComponentTransferFunctionElement", "implements": []}, "SVGFEFuncGElement": {"inherits": "SVGComponentTransferFunctionElement", "implements": []}, "SVGFEFuncRElement": {"inherits": "SVGComponentTransferFunctionElement", "implements": []}, "SVGFEGaussianBlurElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGFEImageElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes", "SVGURIReference"]}, "SVGFEMergeElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGFEMergeNodeElement": {"inherits": "SVGElement", "implements": []}, "SVGFEMorphologyElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGFEOffsetElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGFEPointLightElement": {"inherits": "SVGElement", "implements": []}, "SVGFESpecularLightingElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGFESpotLightElement": {"inherits": "SVGElement", "implements": []}, "SVGFETileElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGFETurbulenceElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGFilterElement": {"inherits": "SVGElement", "implements": ["SVGURIReference", "SVGUnitTypes"]}, "SVGForeignObjectElement": {"inherits": "SVGGraphicsElement", "implements": []}, "SVGGElement": {"inherits": "SVGGraphicsElement", "implements": []}, "SVGGeometryElement": {"inherits": "SVGGraphicsElement", "implements": []}, "SVGGradientElement": {"inherits": "SVGElement", "implements": ["SVGURIReference", "SVGUnitTypes"]}, "SVGGraphicsElement": {"inherits": "SVGElement", "implements": ["SVGTests"]}, "SVGImageElement": {"inherits": "SVGGraphicsElement", "implements": ["MozImageLoadingContent", "SVGURIReference"]}, "SVGLengthList": {"inherits": null, "implements": []}, "SVGLineElement": {"inherits": "SVGGeometryElement", "implements": []}, "SVGLinearGradientElement": {"inherits": "SVGGradientElement", "implements": []}, "SVGMPathElement": {"inherits": "SVGElement", "implements": ["SVGURIReference"]}, "SVGMarkerElement": {"inherits": "SVGElement", "implements": ["SVGFitToViewBox"]}, "SVGMaskElement": {"inherits": "SVGElement", "implements": ["SVGUnitTypes"]}, "SVGMetadataElement": {"inherits": "SVGElement", "implements": []}, "SVGNumberList": {"inherits": null, "implements": []}, "SVGPathElement": {"inherits": "SVGGeometryElement", "implements": ["SVGAnimatedPathData"]}, "SVGPathSegArcAbs": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegArcRel": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegClosePath": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegCurvetoCubicAbs": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegCurvetoCubicRel": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegCurvetoCubicSmoothAbs": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegCurvetoCubicSmoothRel": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegCurvetoQuadraticAbs": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegCurvetoQuadraticRel": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegCurvetoQuadraticSmoothAbs": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegCurvetoQuadraticSmoothRel": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegLinetoAbs": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegLinetoHorizontalAbs": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegLinetoHorizontalRel": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegLinetoRel": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegLinetoVerticalAbs": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegLinetoVerticalRel": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegList": {"inherits": null, "implements": []}, "SVGPathSegMovetoAbs": {"inherits": "SVGPathSeg", "implements": []}, "SVGPathSegMovetoRel": {"inherits": "SVGPathSeg", "implements": []}, "SVGPatternElement": {"inherits": "SVGElement", "implements": ["SVGFitToViewBox", "SVGURIReference", "SVGUnitTypes"]}, "SVGPoint": {"inherits": null, "implements": []}, "SVGPointList": {"inherits": null, "implements": []}, "SVGPolygonElement": {"inherits": "SVGGeometryElement", "implements": ["SVGAnimatedPoints"]}, "SVGPolylineElement": {"inherits": "SVGGeometryElement", "implements": ["SVGAnimatedPoints"]}, "SVGPreserveAspectRatio": {"inherits": null, "implements": []}, "SVGRadialGradientElement": {"inherits": "SVGGradientElement", "implements": []}, "SVGRect": {"inherits": null, "implements": []}, "SVGRectElement": {"inherits": "SVGGeometryElement", "implements": []}, "SVGSVGElement": {"inherits": "SVGGraphicsElement", "implements": ["SVGFitToViewBox", "SVGZoomAndPan"]}, "SVGScriptElement": {"inherits": "SVGElement", "implements": ["SVGURIReference"]}, "SVGSetElement": {"inherits": "SVGAnimationElement", "implements": []}, "SVGStopElement": {"inherits": "SVGElement", "implements": []}, "SVGStringList": {"inherits": null, "implements": []}, "SVGStyleElement": {"inherits": "SVGElement", "implements": []}, "SVGSwitchElement": {"inherits": "SVGGraphicsElement", "implements": []}, "SVGSymbolElement": {"inherits": "SVGElement", "implements": ["SVGFitToViewBox", "SVGTests"]}, "SVGTSpanElement": {"inherits": "SVGTextPositioningElement", "implements": []}, "SVGTextContentElement": {"inherits": "SVGGraphicsElement", "implements": []}, "SVGTextElement": {"inherits": "SVGTextPositioningElement", "implements": []}, "SVGTextPathElement": {"inherits": "SVGTextContentElement", "implements": ["SVGURIReference"]}, "SVGTextPositioningElement": {"inherits": "SVGTextContentElement", "implements": []}, "SVGTitleElement": {"inherits": "SVGElement", "implements": []}, "SVGTransformList": {"inherits": null, "implements": []}, "SVGUseElement": {"inherits": "SVGGraphicsElement", "implements": ["SVGURIReference"]}, "SVGViewElement": {"inherits": "SVGElement", "implements": ["SVGFitToViewBox", "SVGZoomAndPan"]}, "SVGZoomEvent": {"inherits": "UIEvent", "implements": []}, "Screen": {"inherits": "EventTarget", "implements": []}, "ScriptProcessorNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "ScrollAreaEvent": {"inherits": "UIEvent", "implements": []}, "ScrollBoxObject": {"inherits": "BoxObject", "implements": []}, "ScrollViewChangeEvent": {"inherits": "Event", "implements": []}, "Selection": {"inherits": null, "implements": []}, "SelectionStateChangedEvent": {"inherits": "Event", "implements": []}, "Sensor": {"inherits": "EventTarget", "implements": []}, "SensorErrorEvent": {"inherits": "Event", "implements": []}, "ServiceWorker": {"inherits": "EventTarget", "implements": ["AbstractWorker"]}, "ServiceWorkerContainer": {"inherits": "EventTarget", "implements": []}, "ServiceWorkerGlobalScope": {"inherits": "WorkerGlobalScope", "implements": ["GlobalFetch"]}, "ServiceWorkerRegistration": {"inherits": "EventTarget", "implements": []}, "SettingsLock": {"inherits": "EventTarget", "implements": []}, "SettingsManager": {"inherits": "EventTarget", "implements": []}, "ShadowRoot": {"inherits": "DocumentFragment", "implements": []}, "SharedWorker": {"inherits": "EventTarget", "implements": ["AbstractWorker"]}, "SharedWorkerGlobalScope": {"inherits": "WorkerGlobalScope", "implements": []}, "SimpleGestureEvent": {"inherits": "MouseEvent", "implements": []}, "SourceBuffer": {"inherits": "EventTarget", "implements": []}, "SourceBufferList": {"inherits": "EventTarget", "implements": []}, "SpeechRecognition": {"inherits": "EventTarget", "implements": []}, "SpeechRecognitionError": {"inherits": "Event", "implements": []}, "SpeechRecognitionEvent": {"inherits": "Event", "implements": []}, "SpeechSynthesisEvent": {"inherits": "Event", "implements": []}, "SpeechSynthesisUtterance": {"inherits": "EventTarget", "implements": []}, "StereoPannerNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "StorageEvent": {"inherits": "Event", "implements": []}, "StylePropertyMap": {"inherits": "StylePropertyMapReadOnly", "implements": []}, "StylePropertyMapReadOnly": {"inherits": null, "implements": ["StylePropertyMap"]}, "StyleRuleChangeEvent": {"inherits": "Event", "implements": []}, "StyleSheet": {"inherits": null, "implements": []}, "StyleSheetApplicableStateChangeEvent": {"inherits": "Event", "implements": []}, "StyleSheetChangeEvent": {"inherits": "Event", "implements": []}, "SyncEvent": {"inherits": "ExtendableEvent", "implements": []}, "TVChannel": {"inherits": "EventTarget", "implements": []}, "TVCurrentChannelChangedEvent": {"inherits": "Event", "implements": []}, "TVCurrentSourceChangedEvent": {"inherits": "Event", "implements": []}, "TVEITBroadcastedEvent": {"inherits": "Event", "implements": []}, "TVManager": {"inherits": "EventTarget", "implements": []}, "TVScanningStateChangedEvent": {"inherits": "Event", "implements": []}, "TVSource": {"inherits": "EventTarget", "implements": []}, "TVTuner": {"inherits": "EventTarget", "implements": []}, "TaskAttributionTiming": {"inherits": "PerformanceEntry", "implements": []}, "Telephony": {"inherits": "EventTarget", "implements": []}, "TelephonyCall": {"inherits": "EventTarget", "implements": []}, "TelephonyCallGroup": {"inherits": "EventTarget", "implements": []}, "Text": {"inherits": "CharacterData", "implements": ["GeometryUtils"]}, "TextTrack": {"inherits": "EventTarget", "implements": []}, "TextTrackList": {"inherits": "EventTarget", "implements": []}, "TimeEvent": {"inherits": "Event", "implements": []}, "Touch": {"inherits": null, "implements": []}, "TouchEvent": {"inherits": "UIEvent", "implements": []}, "TouchList": {"inherits": null, "implements": []}, "TrackEvent": {"inherits": "Event", "implements": []}, "TransitionEvent": {"inherits": "Event", "implements": []}, "TreeBoxObject": {"inherits": "BoxObject", "implements": []}, "TreeColumns": {"inherits": null, "implements": []}, "TreeWalker": {"inherits": null, "implements": []}, "UDPMessageEvent": {"inherits": "Event", "implements": []}, "UDPSocket": {"inherits": "EventTarget", "implements": []}, "UIEvent": {"inherits": "Event", "implements": []}, "URL": {"inherits": null, "implements": ["URLUtils", "URLUtilsSearchParams"]}, "USSDReceivedEvent": {"inherits": "Event", "implements": []}, "UndoManager": {"inherits": null, "implements": []}, "UserProximityEvent": {"inherits": "Event", "implements": []}, "VRFieldOfView": {"inherits": "VRFieldOfViewReadOnly", "implements": []}, "VTTCue": {"inherits": "EventTarget", "implements": []}, "ValidityState": {"inherits": null, "implements": []}, "VideoStreamTrack": {"inherits": "MediaStreamTrack", "implements": []}, "VideoTrackList": {"inherits": "EventTarget", "implements": []}, "WaveShaperNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "WebGL2RenderingContext": {"inherits": "WebGLRenderingContext", "implements": []}, "WebGLContextEvent": {"inherits": "Event", "implements": []}, "WebSocket": {"inherits": "EventTarget", "implements": []}, "WheelEvent": {"inherits": "MouseEvent", "implements": []}, "Window": {"inherits": null, "implements": ["ChromeWindow", "GlobalCrypto", "GlobalEventHandlers", "OnErrorEventHandlerForWindow", "SpeechSynthesisGetter", "TouchEventHandlers", "WindowEventHandlers", "WindowModal", "WindowOrWorkerGlobalScope"]}, "WindowClient": {"inherits": "Client", "implements": []}, "WindowRoot": {"inherits": "EventTarget", "implements": []}, "Worker": {"inherits": "EventTarget", "implements": ["AbstractWorker"]}, "WorkerGlobalScope": {"inherits": "EventTarget", "implements": ["GlobalCrypto", "WindowOrWorkerGlobalScope"]}, "WorkerLocation": {"inherits": null, "implements": ["URLUtilsReadOnly"]}, "WorkerNavigator": {"inherits": null, "implements": ["NavigatorDataStore", "NavigatorID", "NavigatorLanguage", "NavigatorOnLine"]}, "XMLDocument": {"inherits": "Document", "implements": []}, "XMLHttpRequest": {"inherits": "XMLHttpRequestEventTarget", "implements": []}, "XMLHttpRequestEventTarget": {"inherits": "EventTarget", "implements": []}, "XMLHttpRequestUpload": {"inherits": "XMLHttpRequestEventTarget", "implements": []}, "XMLSerializer": {"inherits": null, "implements": []}, "XMLStylesheetProcessingInstruction": {"inherits": "ProcessingInstruction", "implements": []}, "XPathEvaluator": {"inherits": null, "implements": []}, "XR": {"inherits": "EventTarget", "implements": []}, "XRBoundedReferenceSpace": {"inherits": "XRReferenceSpace", "implements": []}, "XRFrame": {"inherits": null, "implements": []}, "XRInputSource": {"inherits": null, "implements": []}, "XRInputSourceArray": {"inherits": null, "implements": []}, "XRInputSourceEvent": {"inherits": "Event", "implements": []}, "XRInputSourcesChangeEvent": {"inherits": "Event", "implements": []}, "XRPose": {"inherits": null, "implements": []}, "XRReferenceSpace": {"inherits": "XRSpace", "implements": []}, "XRReferenceSpaceEvent": {"inherits": "Event", "implements": []}, "XRRenderState": {"inherits": null, "implements": []}, "XRRigidTransform": {"inherits": null, "implements": []}, "XRSession": {"inherits": "EventTarget", "implements": []}, "XRSessionEvent": {"inherits": "Event", "implements": []}, "XRSpace": {"inherits": "EventTarget", "implements": []}, "XRView": {"inherits": null, "implements": []}, "XRViewerPose": {"inherits": "<PERSON><PERSON><PERSON>", "implements": []}, "XRViewport": {"inherits": null, "implements": []}, "XRWebGLLayer": {"inherits": null, "implements": []}, "XULCommandEvent": {"inherits": "UIEvent", "implements": []}, "XULDocument": {"inherits": "Document", "implements": []}, "XULElement": {"inherits": "Element", "implements": ["GlobalEventHandlers", "MozFrameLoaderOwner", "OnErrorEventHandlerForNodes", "TouchEventHandlers"]}}