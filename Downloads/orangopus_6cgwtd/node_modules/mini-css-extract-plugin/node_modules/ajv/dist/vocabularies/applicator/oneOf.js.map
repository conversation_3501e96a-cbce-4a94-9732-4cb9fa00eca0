{"version": 3, "file": "oneOf.js", "sourceRoot": "", "sources": ["../../../lib/vocabularies/applicator/oneOf.ts"], "names": [], "mappings": ";;AAOA,mDAA6C;AAC7C,6CAAoD;AASpD,MAAM,KAAK,GAA2B;IACpC,OAAO,EAAE,wCAAwC;IACjD,MAAM,EAAE,CAAC,EAAC,MAAM,EAAC,EAAE,EAAE,CAAC,IAAA,WAAC,EAAA,oBAAoB,MAAM,CAAC,OAAO,GAAG;CAC7D,CAAA;AAED,MAAM,GAAG,GAA0B;IACjC,OAAO,EAAE,OAAO;IAChB,UAAU,EAAE,OAAO;IACnB,WAAW,EAAE,IAAI;IACjB,KAAK;IACL,IAAI,CAAC,GAAe;QAClB,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;QAC3C,wBAAwB;QACxB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QACvE,IAAI,EAAE,CAAC,IAAI,CAAC,aAAa,IAAI,YAAY,CAAC,aAAa;YAAE,OAAM;QAC/D,MAAM,MAAM,GAAgB,MAAM,CAAA;QAClC,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;QACrC,MAAM,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;QACxC,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACnC,GAAG,CAAC,SAAS,CAAC,EAAC,OAAO,EAAC,CAAC,CAAA;QACxB,2GAA2G;QAE3G,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;QAExB,GAAG,CAAC,MAAM,CACR,KAAK,EACL,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,EACjB,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CACtB,CAAA;QAED,SAAS,aAAa;YACpB,MAAM,CAAC,OAAO,CAAC,CAAC,GAAc,EAAE,CAAS,EAAE,EAAE;gBAC3C,IAAI,MAA6B,CAAA;gBACjC,IAAI,IAAA,wBAAiB,EAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC;oBAC/B,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;gBACzB,CAAC;qBAAM,CAAC;oBACN,MAAM,GAAG,GAAG,CAAC,SAAS,CACpB;wBACE,OAAO,EAAE,OAAO;wBAChB,UAAU,EAAE,CAAC;wBACb,aAAa,EAAE,IAAI;qBACpB,EACD,QAAQ,CACT,CAAA;gBACH,CAAC;gBAED,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;oBACV,GAAG;yBACA,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,QAAQ,OAAO,KAAK,EAAE,CAAC;yBAC9B,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC;yBACpB,MAAM,CAAC,OAAO,EAAE,IAAA,WAAC,EAAA,IAAI,OAAO,KAAK,CAAC,GAAG,CAAC;yBACtC,IAAI,EAAE,CAAA;gBACX,CAAC;gBAED,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;oBACpB,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;oBACvB,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;oBACtB,IAAI,MAAM;wBAAE,GAAG,CAAC,cAAc,CAAC,MAAM,EAAE,cAAI,CAAC,CAAA;gBAC9C,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAED,kBAAe,GAAG,CAAA"}